#!/usr/bin/env python3
"""
Multi-GPU SAE Training Tutorial

This script demonstrates how to properly train SAEs using multiple GPUs
with DistributedDataParallel for optimal performance.
"""

import os
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP
import logging
from pathlib import Path

# ITAS imports
from itas import (
    SAEConfig,
    ModelConfig,
    DatasetConfig,
    TrainingConfig,
    UniversalModelLoader,
    DatasetManager,
    validate_config,
)
from itas.core.sae import TrainingSAE
from itas.core.activations_store import ActivationsStore

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_distributed(rank, world_size):
    """Initialize distributed training."""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    
    # Initialize the process group
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    torch.cuda.set_device(rank)


def cleanup_distributed():
    """Clean up distributed training."""
    dist.destroy_process_group()


def create_multi_gpu_config(rank, world_size):
    """Create SAE configuration optimized for multi-GPU training."""
    
    # Model configuration - use single device per process
    model_config = ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct",
        use_flash_attention=True,
        torch_dtype="bfloat16",
        device_map={"": f"cuda:{rank}"},  # Single device per process
        trust_remote_code=False,
    )
    
    # Dataset configuration
    dataset_config = DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"},
        dataset_split="train",
        text_column="text",
        max_seq_length=2048,
        chunk_size=2048,
        streaming=False,
        num_proc=4,
        trust_remote_code=False,
    )
    
    # Training configuration - adjust batch size for multi-GPU
    base_batch_size = 4096
    per_gpu_batch_size = base_batch_size // world_size
    
    training_config = TrainingConfig(
        total_training_tokens=50_000_000,  # 50M tokens
        batch_size=per_gpu_batch_size,  # Divide by number of GPUs
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,
        checkpoint_every_n_tokens=10_000_000,
        save_checkpoint_dir="./checkpoints_multi_gpu",
        log_every_n_steps=100,
        eval_every_n_tokens=5_000_000,
        use_wandb=(rank == 0),  # Only log from rank 0
        wandb_project="llama-sae-multi-gpu",
    )
    
    # SAE configuration
    config = SAEConfig(
        model=model_config,
        dataset=dataset_config,
        training=training_config,
        architecture="gated",
        expansion_factor=32,
        hook_layer=16,  # Middle layer
        hook_name="model.layers.{layer}.mlp.down_proj",  # Will be auto-detected
        activation_fn="relu",
        normalize_decoder=True,
        device=f"cuda:{rank}",
        dtype="float32",
        seed=42,
    )
    
    return config


class MultiGPUSAETrainer:
    """Multi-GPU SAE trainer using DistributedDataParallel."""
    
    def __init__(self, config, rank, world_size):
        self.config = config
        self.rank = rank
        self.world_size = world_size
        self.device = f"cuda:{rank}"
        
        # Initialize components
        self.model = None
        self.tokenizer = None
        self.sae = None
        self.optimizer = None
        self.dataset_manager = None
        
    def setup(self):
        """Setup model, SAE, and data components."""
        if self.rank == 0:
            print("🔧 Setting up multi-GPU SAE training...")
            
        # Load model and tokenizer
        model_loader = UniversalModelLoader(self.config.model)
        self.model, self.tokenizer = model_loader.load_model_and_tokenizer()
        
        # Get correct hook names
        hook_names = model_loader.get_hook_names()
        if "mlp_out" in hook_names:
            mlp_hook_pattern = hook_names["mlp_out"]
            self.config.hook_name = mlp_hook_pattern.format(layer=self.config.hook_layer)
        
        if self.rank == 0:
            print(f"✓ Model loaded on rank {self.rank}")
            print(f"  Hook name: {self.config.hook_name}")
            
        # Setup dataset
        self.dataset_manager = DatasetManager(self.config.dataset, self.tokenizer)
        dataset = self.dataset_manager.load_dataset()
        processed_dataset = self.dataset_manager.preprocess_dataset()
        
        # Shard dataset across GPUs
        dataset_size = len(processed_dataset)
        per_gpu_size = dataset_size // self.world_size
        start_idx = self.rank * per_gpu_size
        end_idx = start_idx + per_gpu_size if self.rank < self.world_size - 1 else dataset_size
        
        # Create subset for this GPU
        self.dataset_manager.dataset = processed_dataset.select(range(start_idx, end_idx))
        
        if self.rank == 0:
            print(f"✓ Dataset sharded across {self.world_size} GPUs")
            print(f"  Total samples: {dataset_size:,}")
            print(f"  Per GPU: {per_gpu_size:,}")
            
        # Initialize SAE
        model_info = model_loader.get_model_info()
        d_in = model_info['hidden_size']
        
        self.sae = TrainingSAE(
            d_in=d_in,
            d_sae=d_in * self.config.expansion_factor,
            architecture=self.config.architecture,
            activation_fn=self.config.activation_fn,
            normalize_decoder=self.config.normalize_decoder,
            device=self.device,
            dtype=getattr(torch, self.config.dtype),
        )
        
        # Wrap SAE with DDP
        self.sae = DDP(self.sae, device_ids=[self.rank])
        
        # Initialize optimizer
        self.optimizer = torch.optim.Adam(
            self.sae.parameters(),
            lr=self.config.training.learning_rate,
            betas=(0.9, 0.999),
            weight_decay=0.0,
        )
        
        if self.rank == 0:
            print(f"✓ SAE initialized with DDP")
            print(f"  d_in: {d_in}")
            print(f"  d_sae: {d_in * self.config.expansion_factor}")
            print(f"  Architecture: {self.config.architecture}")
    
    def train(self):
        """Train the SAE using distributed training."""
        if self.rank == 0:
            print("🏋️ Starting distributed SAE training...")
            
        # Setup activations store
        activations_store = ActivationsStore(
            self.model,
            self.tokenizer,
            self.config,
            self.dataset_manager,
        )
        
        # Training parameters
        total_tokens = self.config.training.total_training_tokens
        batch_size = self.config.training.batch_size
        total_steps = total_tokens // (batch_size * self.world_size)
        
        if self.rank == 0:
            print(f"  Total tokens: {total_tokens:,}")
            print(f"  Batch size per GPU: {batch_size}")
            print(f"  Total steps: {total_steps:,}")
            print(f"  World size: {self.world_size}")
        
        # Training loop
        step = 0
        tokens_processed = 0
        
        try:
            with activations_store:
                activations_store.start_streaming(batch_size=32)
                
                while tokens_processed < total_tokens and step < total_steps:
                    # Get batch of activations
                    activations = activations_store.get_next_batch(timeout=10.0)
                    if activations is None:
                        continue
                    
                    # Move to device and prepare
                    activations = activations.to(self.device).float()
                    if len(activations.shape) > 2:
                        activations = activations.view(-1, activations.shape[-1])
                    
                    # Limit batch size
                    if activations.shape[0] > batch_size:
                        activations = activations[:batch_size]
                    
                    # Training step
                    self.optimizer.zero_grad()
                    
                    # Forward pass
                    output = self.sae(activations)
                    
                    # Compute loss
                    l1_coeff = self.config.training.l1_coefficient
                    total_loss = output.mse_loss + l1_coeff * output.l1_loss
                    
                    # Backward pass
                    total_loss.backward()
                    
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(self.sae.parameters(), max_norm=1.0)
                    
                    # Optimizer step
                    self.optimizer.step()
                    
                    # Update counters
                    step += 1
                    tokens_processed += activations.shape[0] * self.world_size
                    
                    # Logging (only from rank 0)
                    if self.rank == 0 and step % self.config.training.log_every_n_steps == 0:
                        print(f"Step {step:,}/{total_steps:,} | "
                              f"Tokens: {tokens_processed:,}/{total_tokens:,} | "
                              f"Loss: {total_loss.item():.6f} | "
                              f"MSE: {output.mse_loss.item():.6f} | "
                              f"L1: {output.l1_loss.item():.6f}")
                
                activations_store.stop_streaming()
                
        except Exception as e:
            logger.error(f"Training failed on rank {self.rank}: {e}")
            raise
        
        if self.rank == 0:
            print("✅ Multi-GPU training completed!")
            
        return self.sae.module  # Return unwrapped SAE


def train_worker(rank, world_size):
    """Worker function for distributed training."""
    try:
        # Setup distributed training
        setup_distributed(rank, world_size)
        
        # Create configuration
        config = create_multi_gpu_config(rank, world_size)
        
        # Validate configuration (only on rank 0)
        if rank == 0:
            issues = validate_config(config)
            if issues:
                print(f"❌ Configuration issues: {issues}")
                return
            print("✓ Configuration validated")
        
        # Initialize trainer
        trainer = MultiGPUSAETrainer(config, rank, world_size)
        trainer.setup()
        
        # Train SAE
        sae = trainer.train()
        
        # Save model (only from rank 0)
        if rank == 0:
            save_path = f"llama_3_1_8b_layer16_gated_sae_multi_gpu.pt"
            torch.save({
                'sae_state_dict': sae.state_dict(),
                'config': config.to_dict(),
            }, save_path)
            print(f"📁 SAE saved to: {save_path}")
        
    except Exception as e:
        logger.error(f"Worker {rank} failed: {e}")
        raise
    finally:
        cleanup_distributed()


def main():
    """Main function to launch distributed training."""
    # Set CUDA devices
    os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
    os.environ["CUDA_VISIBLE_DEVICES"] = "4,5,6,7"  # Use GPUs 4-7
    
    world_size = torch.cuda.device_count()
    print(f"🚀 Launching multi-GPU SAE training on {world_size} GPUs")
    print(f"Available GPUs: {list(range(world_size))}")
    
    if world_size < 2:
        print("❌ Multi-GPU training requires at least 2 GPUs")
        return
    
    # Launch distributed training
    mp.spawn(train_worker, args=(world_size,), nprocs=world_size, join=True)


if __name__ == "__main__":
    main()
