# Install ITAS if not already installed
# !pip install itas

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
import logging

# ITAS imports
import itas
from itas import (
    SAEConfig, ModelConfig, DatasetConfig, TrainingConfig,
    UniversalModelLoader, DatasetManager, validate_config
)
from itas.core.sae import TrainingSAE
from itas.core.activations_store import ActivationsStore
from torch.nn import DataParallel

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_available_gpus():
    """Get list of available GPU device IDs."""
    if not torch.cuda.is_available():
        return []

    # Get CUDA_VISIBLE_DEVICES if set
    visible_devices = os.environ.get("CUDA_VISIBLE_DEVICES")
    if visible_devices:
        # Parse comma-separated device IDs
        try:
            device_ids = [int(d.strip()) for d in visible_devices.split(",") if d.strip()]
            # Map to 0-indexed for torch
            return list(range(len(device_ids)))
        except ValueError:
            # Fallback if parsing fails
            return list(range(torch.cuda.device_count()))
    else:
        return list(range(torch.cuda.device_count()))

def detect_gpu_setup():
    """Detect and configure GPU setup."""
    # Set CUDA devices if not already set
    if "CUDA_VISIBLE_DEVICES" not in os.environ:
        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        os.environ["CUDA_VISIBLE_DEVICES"] = "0,1,2,3"  # Default to first 4 GPUs

    # Check GPU availability
    device = "cuda" if torch.cuda.is_available() else "cpu"
    num_gpus = torch.cuda.device_count()

    print(f"🔧 GPU Setup:")
    print(f"  Using device: {device}")
    print(f"  Available GPUs: {num_gpus}")
    print(f"  CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")

    if device == "cuda":
        for i in range(num_gpus):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"    VRAM: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB")

    return device, num_gpus

# Detect GPU setup
device, num_gpus = detect_gpu_setup()
gpu_ids = get_available_gpus()

# Model configuration
model_name = "meta-llama/Llama-3.1-8B-Instruct"

# Note: You need HuggingFace access to LLaMA models
# Make sure you're logged in: huggingface-cli login

print(f"Loading {model_name}...")
print(f"Using {'multi-GPU' if num_gpus > 1 else 'single-GPU'} setup with {num_gpus} GPU(s)")

# Use balanced device mapping for better memory distribution
device_map = "balanced" if num_gpus > 1 else "auto"

# Load model and tokenizer with optimizations
model_loader = UniversalModelLoader(
    ModelConfig(
        model_name=model_name,
        use_flash_attention=True,  # Automatic compatibility detection
        torch_dtype="bfloat16",    # Memory efficient
        device_map=device_map,     # Optimized device placement
        trust_remote_code=False
    )
)

model, tokenizer = model_loader.load_model_and_tokenizer()

print(f"✓ Model loaded successfully!")
print(f"Model device mapping: {getattr(model, 'hf_device_map', 'Single device')}")
print(f"Model dtype: {next(model.parameters()).dtype}")

# Get detailed model information
model_info = model_loader.get_model_info()
hook_names = model_loader.get_hook_names()

print("📊 Model Information:")
print(f"  Model: {model_info['model_name']}")
print(f"  Architecture: {model_info['architecture']}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  Number of layers: {model_info['num_layers']}")
print(f"  Total parameters: {model_info['total_parameters']:,}")
print(f"  Vocabulary size: {model_info['vocab_size']:,}")

print("\n🔗 Available Hook Points:")
for hook_type, hooks in list(hook_names.items()):
    print(f"  {hook_type}: {len(hooks)} hooks")
    if hooks:
        print(f"    Example: {hooks}")

# Choose a middle layer for SAE training (good balance of complexity and interpretability)
target_layer = model_info['num_layers'] // 2
print(f"\n🎯 Target layer for SAE training: {target_layer}")

# Dataset configuration
dataset_config = DatasetConfig(
    dataset_name="wikitext",
    dataset_kwargs={"name": "wikitext-2-raw-v1"},  # Specify WikiText variant
    dataset_split="train",
    text_column="text",
    max_seq_length=2048,  # LLaMA 3.1 context length
    chunk_size=2048,
    streaming=False,  # Load full dataset for tutorial
    num_proc=4,  # Parallel processing
    trust_remote_code=False,
)

print("📚 Loading and preprocessing dataset...")

# Initialize dataset manager
dataset_manager = DatasetManager(dataset_config, tokenizer)

# Load and preprocess dataset
dataset = dataset_manager.load_dataset()
processed_dataset = dataset_manager.preprocess_dataset()

# Get dataset statistics
dataset_info = dataset_manager.get_dataset_info()
print(f"✓ Dataset loaded successfully!")
print(f" Dataset: {dataset_info['dataset_name']}")
print(f"  Raw size: {dataset_info['raw_size']:,} examples")
print(f"  Processed size: {dataset_info['processed_size']:,} examples")

total_tokens_val = dataset_info.get('total_tokens', 'Unknown')
if isinstance(total_tokens_val, (int, float)):
    print(f"  Total tokens: {total_tokens_val:,}")
else:
    print(f"  Total tokens: {total_tokens_val}")

# Show a sample
sample = processed_dataset[0]
print(f"\n📝 Sample text (first 200 chars):")

input_ids_value = sample.get('input_ids')

if input_ids_value is None:
    print("'(Sample is missing input_ids)'")
# Check if it's a tensor-like object with an 'ndim' attribute (like PyTorch/TensorFlow tensors)
elif hasattr(input_ids_value, 'ndim') and input_ids_value.ndim == 0:
    print(f"Debug: input_ids is a 0-dim tensor. Value: {input_ids_value.item() if hasattr(input_ids_value, 'item') else input_ids_value}")
    # To decode a single token, it often needs to be in a sequence (e.g., a list or 1D tensor)
    # For PyTorch tensor:
    if hasattr(input_ids_value, 'unsqueeze'):
        tokens_to_decode = input_ids_value.unsqueeze(0) # Convert tensor(X) to tensor([X])
        print(f"'{tokenizer.decode(tokens_to_decode)}...' (Note: input_ids was a 0-dim tensor)")
    else: # Fallback if not a PyTorch tensor but still 0-dim somehow
        print(f"'{tokenizer.decode([input_ids_value])}...' (Note: input_ids was a 0-dim value, attempting decode)")
elif hasattr(input_ids_value, '__len__') and len(input_ids_value) > 0: # List or 1D tensor with elements
    tokens_to_show = input_ids_value[:50]
    print(f"'{tokenizer.decode(tokens_to_show)}...'")
else: # Empty list, empty tensor, or other unexpected type
    print("'(Sample input_ids is empty or not in a decodable format)'")


# Get the correct hook names for this model
hook_names = model_loader.get_hook_names()
mlp_hook_pattern = hook_names['mlp_out']
mlp_hook_name = mlp_hook_pattern.format(layer=target_layer)

print(f"🔗 Available hook patterns: {list(hook_names.keys())}")
print(f"🎯 Using MLP hook: {mlp_hook_name}")

# Create comprehensive SAE configuration
config = SAEConfig(
    # Model configuration
    model=ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        trust_remote_code=False,
        device_map="auto",
    ),

    # Dataset configuration
    dataset=dataset_config,

    # Training configuration
    training=TrainingConfig(
        total_training_tokens=50_000_000,  # 50M tokens for tutorial
        batch_size=4096 * max(1, num_gpus // 2),  # Scaled for multi-GPU
        learning_rate=3e-4,
        l1_coefficient=1e-3,               # Sparsity regularization
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,

        # Checkpointing and logging
        checkpoint_every_n_tokens=10_000_000,
        save_checkpoint_dir="./checkpoints",
        log_every_n_steps=100,
        eval_every_n_tokens=5_000_000,

        # No W&B for tutorial
        use_wandb=False,
    ),

    # SAE architecture
    architecture="gated",              # Start with gated SAE (best performance)
    expansion_factor=32,               # 32x expansion (4096 -> 131,072 features)
    hook_layer=target_layer,           # Middle layer
    hook_name=mlp_hook_name,           # Use correct hook name for this model
    activation_fn="relu",
    normalize_decoder=True,

    # Device and precision
    device="cuda:0",                   # Primary device
    dtype="float32",                   # Training precision
    seed=42,
)

print("\n⚙️ SAE Configuration:")
print(f"  Architecture: {config.architecture}")
print(f"  Hook layer: {config.hook_layer}")
print(f"  Hook name: {config.hook_name}")
print(f"  Expansion factor: {config.expansion_factor}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  SAE features: {model_info['hidden_size'] * config.expansion_factor:,}")
print(f"  Training tokens: {config.training.total_training_tokens:,}")
print(f"  Batch size: {config.training.batch_size}")

# Validate configuration
try:
    issues = validate_config(config)
    if issues:
        print(f"❌ Configuration issues found: {issues}")
        raise ValueError(f"Invalid configuration: {issues}")
    else:
        print("✓ Configuration is valid!")
except Exception as e:
    print(f"❌ Configuration error: {e}")
    raise

class SAETrainer:
    """Scalable SAE trainer that works with single or multiple GPUs using DataParallel."""

    def __init__(self, config, gpu_ids=None):
        self.config = config
        self.gpu_ids = gpu_ids or [0]
        self.num_gpus = len(self.gpu_ids)
        self.device = f"cuda:{self.gpu_ids[0]}" if torch.cuda.is_available() else "cpu"

        # Initialize components
        self.model = None
        self.tokenizer = None
        self.sae = None
        self.optimizer = None
        self.dataset_manager = None

    def setup(self):
        """Setup model, SAE, and data components."""
        print(
            f"🔧 Setting up {'multi-GPU' if self.num_gpus > 1 else 'single-GPU'} SAE training..."
        )
        print(f"  Using GPUs: {self.gpu_ids}")

        # Load model and tokenizer
        model_loader = UniversalModelLoader(self.config.model)
        self.model, self.tokenizer = model_loader.load_model_and_tokenizer()

        # Get model info and update config
        model_info = model_loader.get_model_info()
        hook_names = model_loader.get_hook_names()

        # Update target layer and hook name
        target_layer = model_info["num_layers"] // 2
        self.config.hook_layer = target_layer

        if "mlp_out" in hook_names:
            mlp_hook_pattern = hook_names["mlp_out"]
            self.config.hook_name = mlp_hook_pattern.format(layer=target_layer)

        print(f"✓ Model loaded: {model_info['model_name']}")
        print(f"  Architecture: {model_info['architecture']}")
        print(f"  Hidden size: {model_info['hidden_size']}")
        print(f"  Layers: {model_info['num_layers']}")
        print(f"  Target layer: {target_layer}")
        print(f"  Hook name: {self.config.hook_name}")

        # Setup dataset
        self.dataset_manager = DatasetManager(self.config.dataset, self.tokenizer)
        self.dataset_manager.load_dataset()
        self.dataset_manager.preprocess_dataset()

        dataset_info = self.dataset_manager.get_dataset_info()
        print(f"✓ Dataset loaded: {dataset_info['processed_size']:,} samples")

        # Initialize SAE
        d_in = model_info["hidden_size"]

        self.sae = TrainingSAE(
            d_in=d_in,
            d_sae=d_in * self.config.expansion_factor,
            architecture=self.config.architecture,
            activation_fn=self.config.activation_fn,
            normalize_decoder=self.config.normalize_decoder,
            device=self.device,
            dtype=getattr(torch, self.config.dtype),
        )

        # Wrap SAE with DataParallel if multi-GPU
        if self.num_gpus > 1:
            print(f"🔥 Enabling DataParallel across {self.num_gpus} GPUs")
            self.sae = DataParallel(self.sae, device_ids=self.gpu_ids)

        # Initialize optimizer
        self.optimizer = torch.optim.Adam(
            self.sae.parameters(),
            lr=self.config.training.learning_rate,
            betas=(0.9, 0.999),
            weight_decay=0.0,
        )

        print(f"✓ SAE initialized")
        print(f"  d_in: {d_in}")
        print(f"  d_sae: {d_in * self.config.expansion_factor}")
        print(f"  Architecture: {self.config.architecture}")
        print(f"  Multi-GPU: {self.num_gpus > 1}")
        print(f"  Effective batch size: {self.config.training.batch_size}")

        # Initialize W&B if enabled
        if self.config.training.use_wandb:
            wandb.init(
                project=self.config.training.wandb_project,
                name=self.config.training.wandb_run_name,
                config={
                    "model_name": self.config.model.model_name,
                    "dataset_name": self.config.dataset.dataset_name,
                    "architecture": self.config.architecture,
                    "expansion_factor": self.config.expansion_factor,
                    "hook_layer": self.config.hook_layer,
                    "hook_name": self.config.hook_name,
                    "d_in": d_in,
                    "d_sae": d_in * self.config.expansion_factor,
                    "total_training_tokens": self.config.training.total_training_tokens,
                    "batch_size": self.config.training.batch_size,
                    "learning_rate": self.config.training.learning_rate,
                    "l1_coefficient": self.config.training.l1_coefficient,
                    "num_gpus": self.num_gpus,
                    "gpu_ids": self.gpu_ids,
                },
            )
            print(
                f"✓ W&B initialized: {self.config.training.wandb_project}/{self.config.training.wandb_run_name}"
            )

    def train(self):
        """Train the SAE."""
        print("🏋️ Starting SAE training...")

        # Setup activations store
        activations_store = ActivationsStore(
            self.model,
            self.tokenizer,
            self.config,
            self.dataset_manager,
        )

        # Training parameters
        total_tokens = self.config.training.total_training_tokens
        batch_size = self.config.training.batch_size
        total_steps = total_tokens // batch_size

        print(f"  Total tokens: {total_tokens:,}")
        print(f"  Batch size: {batch_size}")
        print(f"  Total steps: {total_steps:,}")
        print(f"  GPUs: {self.num_gpus}")

        # Estimate training time
        tokens_per_second_estimate = 1000 * self.num_gpus  # Scale with GPUs
        estimated_hours = total_tokens / tokens_per_second_estimate / 3600
        print(f"  Estimated time: {estimated_hours:.1f} hours")

        # Training loop
        step = 0
        tokens_processed = 0

        try:
            with activations_store:
                activations_store.start_streaming(batch_size=32)

                while tokens_processed < total_tokens and step < total_steps:
                    # Get batch of activations
                    activations = activations_store.get_next_batch(timeout=10.0)
                    if activations is None:
                        continue

                    # Move to device and prepare
                    activations = activations.to(self.device).float()
                    if len(activations.shape) > 2:
                        activations = activations.view(-1, activations.shape[-1])

                    # Limit batch size
                    if activations.shape[0] > batch_size:
                        activations = activations[:batch_size]

                    # Training step
                    self.optimizer.zero_grad()

                    # Forward pass - DataParallel handles multi-GPU automatically
                    if self.num_gpus > 1:
                        # For DataParallel, we need to use the standard forward method
                        output = self.sae(activations)
                        # Manually compute loss for DataParallel
                        l1_coeff = self.config.training.l1_coefficient
                        total_loss = output.mse_loss + l1_coeff * output.l1_loss
                    else:
                        # Single GPU can use training_forward
                        output = self.sae.training_forward(
                            activations,
                            l1_coefficient=self.config.training.l1_coefficient,
                        )
                        total_loss = output.mse_loss + output.l1_loss
                        if output.aux_loss is not None:
                            total_loss = total_loss + output.aux_loss

                    # Backward pass
                    total_loss.backward()

                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(self.sae.parameters(), max_norm=1.0)

                    # Optimizer step
                    self.optimizer.step()

                    # Update counters
                    step += 1
                    tokens_processed += activations.shape[0]

                    # Logging
                    if step % self.config.training.log_every_n_steps == 0:
                        metrics = {
                            "step": step,
                            "tokens_processed": tokens_processed,
                            "total_loss": total_loss.item(),
                            "mse_loss": output.mse_loss.item(),
                            "l1_loss": output.l1_loss.item(),
                            "progress": tokens_processed / total_tokens,
                        }

                        # Add auxiliary loss if available
                        if hasattr(output, "aux_loss") and output.aux_loss is not None:
                            metrics["aux_loss"] = output.aux_loss.item()

                        # Console logging
                        print(
                            f"Step {step:,}/{total_steps:,} | "
                            f"Tokens: {tokens_processed:,}/{total_tokens:,} | "
                            f"Loss: {total_loss.item():.6f} | "
                            f"MSE: {output.mse_loss.item():.6f} | "
                            f"L1: {output.l1_loss.item():.6f}"
                        )

                        # W&B logging
                        if self.config.training.use_wandb:
                            wandb.log(metrics)

                activations_store.stop_streaming()

        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise

        print("✅ Training completed!")

        # Finish W&B run
        if self.config.training.use_wandb:
            wandb.finish()
            print("✓ W&B run finished")

        # Return the underlying module if wrapped with DataParallel
        return self.sae.module if self.num_gpus > 1 else self.sae

# Train the SAE
print("🏋️ Starting SAE training...")
print(f"Training configuration:")
print(f"  Total tokens: {config.training.total_training_tokens:,}")
print(f"  Batch size: {config.training.batch_size}")
print(f"  Learning rate: {config.training.learning_rate}")
print(f"  L1 coefficient: {config.training.l1_coefficient}")
print(f"  Architecture: {config.architecture}")
print(f"  Hook: {config.hook_name}")
print("\nThis may take 30-60 minutes depending on your hardware...\n")

# Initialize trainer
trainer = SAETrainer(config)

# Train the SAE
sae = trainer.train()

# Save the trained SAE
save_path = f"llama_3_1_8b_layer{target_layer}_gated_sae.pt"
trainer.save_model(save_path)

print(f"\n✅ Training completed successfully!")
print(f"📁 SAE saved to: {save_path}")

# Get training metrics
training_metrics = trainer.get_training_metrics()
print(f"\n📊 Training Summary:")
print(f"  Total steps: {training_metrics['total_steps']}")
print(f"  Total tokens: {training_metrics['total_tokens']:,}")
print(f"  Final loss: {training_metrics['final_loss']:.6f}")
print(f"  Final MSE: {training_metrics['final_mse_loss']:.6f}")
print(f"  Final L1: {training_metrics['final_l1_loss']:.6f}")
print(f"  Final sparsity: {training_metrics['final_sparsity']:.4f}")
print(f"  Final FVU: {training_metrics['final_fvu']:.4f}")

# Quick evaluation of the trained SAE
print("📊 Evaluating trained SAE...")

# Get some test activations
test_texts = [
    "The capital of France is Paris.",
    "Machine learning is a subset of artificial intelligence.",
    "The mitochondria is the powerhouse of the cell.",
    "Shakespeare wrote Romeo and Juliet.",
    "Python is a popular programming language."
]

# Tokenize test texts
test_inputs = tokenizer(test_texts, return_tensors="pt", padding=True, truncation=True)
test_inputs = {k: v.to(device) for k, v in test_inputs.items()}

# Get activations from the model
with torch.no_grad():
    outputs = model(**test_inputs, output_hidden_states=True)
    # Extract activations from our target layer
    test_activations = outputs.hidden_states[target_layer]  # Shape: [batch, seq_len, hidden_size]
    # Flatten to [batch * seq_len, hidden_size]
    test_activations = test_activations.view(-1, test_activations.size(-1))

print(f"Test activations shape: {test_activations.shape}")

# Test SAE reconstruction
with torch.no_grad():
    # Convert to float32 for SAE processing
    test_activations_f32 = test_activations.float()
    sae_output = sae(test_activations_f32)

    # Extract outputs using correct attribute names
    reconstructed = sae_output.sae_out  # Reconstructed activations
    feature_activations = sae_output.feature_acts  # Feature activations

# Calculate basic metrics
mse = torch.mean((test_activations_f32 - reconstructed) ** 2).item()
cosine_sim = torch.nn.functional.cosine_similarity(
    test_activations_f32.flatten(), reconstructed.flatten(), dim=0
).item()
sparsity = (feature_activations == 0).float().mean().item()

# Calculate Fraction of Variance Unexplained (FVU)
residual = reconstructed - test_activations_f32
total_variance = (test_activations_f32 - test_activations_f32.mean(dim=-1, keepdim=True)).pow(2).sum(dim=-1)
residual_variance = residual.pow(2).sum(dim=-1)
fvu = (residual_variance / (total_variance + 1e-8)).mean().item()

print(f"\n📈 Quick Evaluation Results:")
print(f"  MSE Loss: {sae_output.mse_loss.item():.6f}")
print(f"  L1 Loss: {sae_output.l1_loss.item():.6f}")
print(f"  FVU: {sae_output.fvu.item():.4f}")
print(f"  Cosine Similarity: {cosine_sim:.4f}")
print(f"  Sparsity: {sae_output.sparsity.item():.4f}")
print(f"  Active Features: {(feature_activations > 0).sum(dim=1).float().mean():.1f} / {feature_activations.size(1)}")

# Train different SAE architectures for comparison
architectures = ["standard", "gated", "jumprelu"]
trained_saes = {}
evaluation_results = {}

for arch in architectures:
    print(f"\n🏗️ Training {arch.upper()} SAE...")

    # Create config for this architecture
    arch_config = config.copy()
    arch_config.architecture = arch
    # Use fewer tokens for comparison (faster training)
    arch_config.training.total_training_tokens = 10_000_000  # 10M tokens

    # Train SAE
    trainer = SAETrainer(arch_config)
    sae = trainer.train()

    # Save SAE
    save_path = f"llama_3_1_8b_layer{target_layer}_{arch}_sae.pt"
    trainer.save_model(save_path)
    trained_saes[arch] = sae

    # Quick evaluation
    with torch.no_grad():
        test_activations_f32 = test_activations.float()
        sae_output = sae(test_activations_f32)
        reconstructed = sae_output.sae_out
        feature_activations = sae_output.feature_acts

    mse = sae_output.mse_loss.item()
    cosine_sim = torch.nn.functional.cosine_similarity(
        test_activations_f32.flatten(), reconstructed.flatten(), dim=0
    ).item()
    sparsity = sae_output.sparsity.item()

    evaluation_results[arch] = {
        'mse': mse,
        'cosine_sim': cosine_sim,
        'sparsity': sparsity,
        'active_features': (feature_activations > 0).sum(dim=1).float().mean().item()
    }

    print(f"  ✓ {arch.upper()} SAE completed")
    print(f"    MSE: {mse:.6f}, Cosine Sim: {cosine_sim:.4f}, Sparsity: {sparsity:.4f}")

print("\n🏆 Architecture Comparison Results:")
print("Architecture | MSE      | Cosine Sim | Sparsity | Active Features")
print("-" * 65)
for arch, results in evaluation_results.items():
    print(f"{arch:11} | {results['mse']:.6f} | {results['cosine_sim']:.6f} | {results['sparsity']:.6f} | {results['active_features']:.1f}")

# Function extraction: Context vs Knowledge behavior
print("🔍 Starting function extraction...")

# Use the best performing SAE (typically gated)
best_sae = trained_saes['gated']

# Create function extractor
function_extractor = FunctionExtractor(
    sae=best_sae,
    initialization_method="uniform",
    regularization_strength=1e-5,
    device=device
)

# Define examples for different behaviors
context_based_prompts = [
    "Based on the context provided, the answer is clear.",
    "According to the given information, we can conclude that.",
    "The context clearly states that the solution is.",
    "From the provided text, it's evident that.",
    "The passage indicates that the correct answer is."
]

knowledge_based_prompts = [
    "From my knowledge, I believe the answer is.",
    "Based on what I know, the solution should be.",
    "Generally speaking, this type of problem requires.",
    "In my understanding, the correct approach is.",
    "From general knowledge, we can determine that."
]

# Get activations for both types of prompts
def get_activations_for_prompts(prompts):
    inputs = tokenizer(prompts, return_tensors="pt", padding=True, truncation=True)
    inputs = {k: v.to(device) for k, v in inputs.items()}

    with torch.no_grad():
        outputs = model(**inputs, output_hidden_states=True)
        activations = outputs.hidden_states[target_layer]
        # Take the last token activation for each sequence
        activations = activations[:, -1, :]  # [batch_size, hidden_size]

    return activations

context_activations = get_activations_for_prompts(context_based_prompts)
knowledge_activations = get_activations_for_prompts(knowledge_based_prompts)

print(f"Context activations shape: {context_activations.shape}")
print(f"Knowledge activations shape: {knowledge_activations.shape}")

# Extract function
print("\n🎯 Extracting behavioral function...")
extraction_result = function_extractor.extract_function(
    target_activations=context_activations,
    context_activations=knowledge_activations,
    learning_rate=1e-3,
    num_iterations=1000,
    verbose=True
)

print(f"\n✅ Function extraction completed!")
print(f"  Active features: {len(extraction_result.active_features)}")
print(f"  Extraction strength: {extraction_result.extraction_strength:.6f}")
print(f"  Final loss: {extraction_result.metadata.get('final_loss', 'N/A'):.6f}")

# Analyze feature importance
importance_stats = function_extractor.analyze_feature_importance()
print(f"\n📊 Feature Importance Statistics:")
print(f"  Mean importance: {importance_stats['mean']:.6f}")
print(f"  Std importance: {importance_stats['std']:.6f}")
print(f"  Max importance: {importance_stats['max']:.6f}")

# Get top features
top_features = function_extractor.get_top_features(k=20)
print(f"\n🔝 Top 20 Features: {top_features[:10]}...")

# Create representation engineer
print("🎛️ Setting up representation engineering...")

engineer = RepresentationEngineer(
    model=model,
    tokenizer=tokenizer,
    sae=best_sae,
    hook_layer=target_layer,
    hook_name=mlp_hook_name  # Use the same hook name as SAE training
)

# Create steering vector using our extracted function
print("🧭 Creating steering vector...")
steering_vector = engineer.create_steering_vector(
    positive_examples=context_based_prompts,
    negative_examples=knowledge_based_prompts,
    strength=2.0
)

print(f"✓ Steering vector created with shape: {steering_vector.shape}")

# Test intervention on some prompts
test_prompts = [
    "What is the capital of France?",
    "How does photosynthesis work?",
    "Explain machine learning in simple terms.",
    "What are the benefits of renewable energy?"
]

print("\n🧪 Testing intervention effects...")

# Apply intervention
intervention_fn = engineer.apply_steering_intervention(
    steering_vector,
    strength=1.5,
    layers=[target_layer]
)

# Test intervention effectiveness
results = engineer.test_intervention(
    test_prompts,
    intervention_fn,
    max_new_tokens=50,
    temperature=0.7
)

print("\n📝 Intervention Results:")
print("=" * 80)
for i, (prompt, result) in enumerate(zip(test_prompts, results)):
    print(f"\nPrompt {i+1}: {prompt}")
    print(f"Original:  {result['original_text'][:100]}...")
    print(f"Modified:  {result['modified_text'][:100]}...")
    print("-" * 40)

# Comprehensive SAE evaluation
print("📊 Starting comprehensive SAE evaluation...")

# Create evaluator
evaluator = SAEEvaluator()

# Prepare larger test dataset for evaluation
eval_texts = [
    "The theory of relativity was developed by Einstein.",
    "Machine learning algorithms can learn from data.",
    "The human brain contains billions of neurons.",
    "Climate change affects global weather patterns.",
    "Quantum computers use quantum mechanical phenomena.",
    "DNA contains the genetic instructions for life.",
    "The internet connects computers worldwide.",
    "Renewable energy sources include solar and wind.",
    "Artificial intelligence mimics human intelligence.",
    "The periodic table organizes chemical elements."
]

# Get evaluation activations
eval_inputs = tokenizer(eval_texts, return_tensors="pt", padding=True, truncation=True)
eval_inputs = {k: v.to(device) for k, v in eval_inputs.items()}

with torch.no_grad():
    eval_outputs = model(**eval_inputs, output_hidden_states=True)
    eval_activations = eval_outputs.hidden_states[target_layer]
    eval_activations = eval_activations.view(-1, eval_activations.size(-1))

print(f"Evaluation activations shape: {eval_activations.shape}")

# Comprehensive evaluation for each SAE
comprehensive_results = {}

for arch_name, sae in trained_saes.items():
    print(f"\n🔍 Evaluating {arch_name.upper()} SAE...")

    evaluation = evaluator.evaluate_sae_comprehensive(
        sae=sae,
        test_activations=eval_activations,
        compute_feature_metrics=True,
        compute_reconstruction_metrics=True,
        compute_sparsity_metrics=True
    )

    comprehensive_results[arch_name] = evaluation

    print(f"  Overall Score: {evaluation.overall_score:.4f}")
    print(f"  Reconstruction FVU: {evaluation.reconstruction_metrics['fvu']:.4f}")
    print(f"  Sparsity: {evaluation.sparsity_metrics['overall_sparsity']:.4f}")
    print(f"  Active Features: {evaluation.sparsity_metrics['active_features']}")
    print(f"  Dead Features: {evaluation.sparsity_metrics['dead_features']}")

# Compare all SAEs
print("\n🏆 Final SAE Comparison:")
print("=" * 80)
print(f"{'Architecture':<12} {'Overall Score':<14} {'FVU':<8} {'Sparsity':<10} {'Active Features':<15}")
print("-" * 80)

for arch_name, evaluation in comprehensive_results.items():
    print(f"{arch_name:<12} {evaluation.overall_score:<14.4f} "
          f"{evaluation.reconstruction_metrics['fvu']:<8.4f} "
          f"{evaluation.sparsity_metrics['overall_sparsity']:<10.4f} "
          f"{evaluation.sparsity_metrics['active_features']:<15}")

# Find best performing SAE
best_arch = max(comprehensive_results.keys(),
                key=lambda x: comprehensive_results[x].overall_score)
print(f"\n🥇 Best performing architecture: {best_arch.upper()}")
print(f"   Score: {comprehensive_results[best_arch].overall_score:.4f}")

# Create visualizations
print("📈 Creating visualizations...")

# Initialize visualizer
visualizer = SAEVisualizer()

# Set up matplotlib for better plots
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

# 1. Plot activation distributions
fig1 = visualizer.plot_activation_distribution(
    activations=eval_activations.cpu().numpy(),
    title=f"LLaMA 3.1 8B Layer {target_layer} Activation Distribution",
    save_path="activation_distribution.png"
)
plt.show()

# 2. Plot feature importance from function extraction
if 'extraction_result' in locals():
    fig2 = visualizer.plot_feature_importance(
        importance_scores=extraction_result.feature_weights.cpu().numpy(),
        top_k=50,
        title="Top 50 Features: Context vs Knowledge Behavior",
        save_path="feature_importance.png"
    )
    plt.show()

# 3. Plot SAE architecture comparison
comparison_data = {
    arch: {
        'overall_score': results.overall_score,
        'fvu': results.reconstruction_metrics['fvu'],
        'sparsity': results.sparsity_metrics['overall_sparsity']
    }
    for arch, results in comprehensive_results.items()
}

fig3 = visualizer.plot_sae_comparison(
    comparison_results=comparison_data,
    metrics=["overall_score", "fvu", "sparsity"],
    title="SAE Architecture Comparison on LLaMA 3.1 8B",
    save_path="sae_comparison.png"
)
plt.show()

# 4. Create feature activation heatmap for best SAE
best_sae_obj = trained_saes[best_arch]
with torch.no_grad():
    sample_output = best_sae_obj(eval_activations[:10])  # First 10 samples
    feature_acts = sample_output.feature_acts.cpu().numpy()

# Plot heatmap of top active features
top_features_mask = np.sum(feature_acts > 0, axis=0) > 0  # Features active in at least one sample
active_features = feature_acts[:, top_features_mask]

if active_features.shape[1] > 0:
    plt.figure(figsize=(15, 8))
    plt.imshow(active_features[:, :min(100, active_features.shape[1])].T,
               aspect='auto', cmap='viridis', interpolation='nearest')
    plt.colorbar(label='Feature Activation')
    plt.title(f'Feature Activation Heatmap - {best_arch.upper()} SAE')
    plt.xlabel('Sample Index')
    plt.ylabel('Feature Index')
    plt.tight_layout()
    plt.savefig('feature_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

print("✅ All visualizations created and saved!")
print("📁 Saved files:")
print("  - activation_distribution.png")
print("  - feature_importance.png")
print("  - sae_comparison.png")
print("  - feature_heatmap.png")

# Tutorial summary
print("🎉 Tutorial Complete! Here's what we accomplished:")
print("=" * 60)

print("\n✅ 1. Model Setup:")
print(f"   - Loaded LLaMA 3.1 8B Instruct ({model_info['total_parameters']:,} parameters)")
print(f"   - Explored model architecture and hook points")
print(f"   - Selected layer {target_layer} for SAE training")

print("\n✅ 2. Dataset Preparation:")
print(f"   - Processed WikiText-2 dataset")
print(f"   - Tokenized and chunked text data")
print(f"   - Prepared {dataset_info.get('processed_size', 'N/A')} training examples")

print("\n✅ 3. SAE Training:")
print(f"   - Trained 3 different SAE architectures: Standard, Gated, JumpReLU")
print(f"   - Used {config.expansion_factor}x expansion factor")
print(f"   - Created {model_info['hidden_size'] * config.expansion_factor:,} SAE features")

print("\n✅ 4. Function Extraction:")
if 'extraction_result' in locals():
    print(f"   - Extracted behavioral function with {len(extraction_result.active_features)} active features")
    print(f"   - Achieved extraction strength: {extraction_result.extraction_strength:.6f}")
else:
    print("   - Function extraction framework demonstrated")

print("\n✅ 5. Representation Engineering:")
print("   - Created steering vectors for behavior modification")
print("   - Tested intervention effects on model outputs")
print("   - Demonstrated context vs knowledge steering")

print("\n✅ 6. Comprehensive Evaluation:")
if comprehensive_results:
    print(f"   - Best architecture: {best_arch.upper()}")
    print(f"   - Best score: {comprehensive_results[best_arch].overall_score:.4f}")
    print("   - Compared reconstruction quality, sparsity, and feature metrics")

print("\n✅ 7. Visualization:")
print("   - Created activation distribution plots")
print("   - Visualized feature importance")
print("   - Generated architecture comparison charts")
print("   - Produced feature activation heatmaps")

print("\n📁 Generated Files:")
saved_files = [
    f"llama_3_1_8b_layer{target_layer}_gated_sae.pt",
    f"llama_3_1_8b_layer{target_layer}_standard_sae.pt",
    f"llama_3_1_8b_layer{target_layer}_jumprelu_sae.pt",
    "activation_distribution.png",
    "feature_importance.png",
    "sae_comparison.png",
    "feature_heatmap.png"
]

for file in saved_files:
    print(f"   - {file}")

print("\n🚀 Next Steps:")
print("   1. Experiment with different models (GPT, BERT, Mistral, etc.)")
print("   2. Try different datasets (code, scientific, domain-specific)")
print("   3. Explore different hook points (attention, MLP, residual)")
print("   4. Scale up training with more tokens and larger expansion factors")
print("   5. Implement custom SAE architectures")
print("   6. Apply to real-world representation engineering tasks")
print("   7. Contribute to the SpARE library development")

print("\n📚 Resources:")
print("   - SpARE Documentation: Check the docs/ folder")
print("   - Example Scripts: See demo.py for more examples")
print("   - Research Paper: https://arxiv.org/pdf/2410.15999")
print("   - GitHub Issues: Report bugs and request features")

print("\n🎯 Happy SAE Training with SpARE! 🎯")