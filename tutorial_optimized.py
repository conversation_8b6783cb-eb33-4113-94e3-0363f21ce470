import os
import torch
import logging
from pathlib import Path

# ITAS imports
from itas import (
    SAEConfig,
    ModelConfig,
    DatasetConfig,
    TrainingConfig,
    SAETrainer,
    UniversalModelLoader,
    DatasetManager,
    validate_config,
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set CUDA devices - using GPUs 4,5,6,7 as specified
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
os.environ["CUDA_VISIBLE_DEVICES"] = "4,5,6,7"

# Check GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"
num_gpus = torch.cuda.device_count()
print(f"🔧 GPU Setup:")
print(f"  Using device: {device}")
print(f"  Available GPUs: {num_gpus}")
if device == "cuda":
    for i in range(num_gpus):
        print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
        print(f"    VRAM: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB")

# Model configuration - optimized for multi-GPU
model_name = "meta-llama/Llama-3.1-8B-Instruct"

print(f"\n📦 Loading {model_name}...")

# Load model with balanced device mapping
model_loader = UniversalModelLoader(
    ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",  # Memory efficient
        device_map="balanced",  # Better than "auto" for multi-GPU
        trust_remote_code=False,
    )
)

model, tokenizer = model_loader.load_model_and_tokenizer()

print(f"✓ Model loaded successfully!")
print(f"Model device distribution:")
# Show which layers are on which devices
if hasattr(model, 'hf_device_map'):
    device_map = model.hf_device_map
    device_counts = {}
    for layer, device in device_map.items():
        device_counts[device] = device_counts.get(device, 0) + 1
    for device, count in device_counts.items():
        print(f"  {device}: {count} layers")

# Get model information
model_info = model_loader.get_model_info()
hook_names = model_loader.get_hook_names()

print(f"\n📊 Model Information:")
print(f"  Architecture: {model_info['architecture']}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  Number of layers: {model_info['num_layers']}")
print(f"  Total parameters: {model_info['total_parameters']:,}")

# Choose target layer
target_layer = model_info["num_layers"] // 2
print(f"\n🎯 Target layer for SAE training: {target_layer}")

# Get correct hook name
mlp_hook_pattern = hook_names["mlp_out"]
mlp_hook_name = mlp_hook_pattern.format(layer=target_layer)
print(f"🔗 Using hook: {mlp_hook_name}")

# Dataset configuration - optimized for performance
dataset_config = DatasetConfig(
    dataset_name="wikitext",
    dataset_kwargs={"name": "wikitext-2-raw-v1"},
    dataset_split="train",
    text_column="text",
    max_seq_length=2048,
    chunk_size=2048,
    streaming=False,
    num_proc=min(8, os.cpu_count()),  # Use available CPU cores
    trust_remote_code=False,
)

print(f"\n📚 Loading and preprocessing dataset...")

# Initialize dataset manager
dataset_manager = DatasetManager(dataset_config, tokenizer)
dataset = dataset_manager.load_dataset()
processed_dataset = dataset_manager.preprocess_dataset()

# Get dataset statistics
dataset_info = dataset_manager.get_dataset_info()
print(f"✓ Dataset loaded successfully!")
print(f"  Dataset: {dataset_info['dataset_name']}")
print(f"  Processed size: {dataset_info['processed_size']:,} examples")

# Optimized training configuration
# Increase batch size to better utilize multiple GPUs
# The model is distributed, so we can use larger batches
training_config = TrainingConfig(
    total_training_tokens=50_000_000,  # 50M tokens
    batch_size=8192,  # Increased from 4096 - model is distributed
    learning_rate=3e-4,
    l1_coefficient=1e-3,
    lr_scheduler="cosine",
    lr_warm_up_steps=1000,

    # More frequent checkpointing for long training
    checkpoint_every_n_tokens=5_000_000,  # Every 5M tokens
    save_checkpoint_dir="./checkpoints_optimized",
    log_every_n_steps=50,  # More frequent logging
    eval_every_n_tokens=2_500_000,  # More frequent evaluation

    # Enable W&B for monitoring
    use_wandb=True,
    wandb_project="llama-sae-optimized",
    wandb_run_name=f"llama-3.1-8b-layer{target_layer}-gated-optimized",
)

# Create SAE configuration
config = SAEConfig(
    model=ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        device_map="balanced",
        trust_remote_code=False,
    ),
    dataset=dataset_config,
    training=training_config,

    # SAE architecture
    architecture="gated",  # Best performance
    expansion_factor=32,   # 32x expansion
    hook_layer=target_layer,
    hook_name=mlp_hook_name,
    activation_fn="relu",
    normalize_decoder=True,

    # Device and precision
    device="cuda:0",  # Primary device for SAE
    dtype="float32",  # Training precision
    seed=42,
)

print(f"\n⚙️ SAE Configuration:")
print(f"  Architecture: {config.architecture}")
print(f"  Hook layer: {config.hook_layer}")
print(f"  Hook name: {config.hook_name}")
print(f"  Expansion factor: {config.expansion_factor}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  SAE features: {model_info['hidden_size'] * config.expansion_factor:,}")
print(f"  Training tokens: {config.training.total_training_tokens:,}")
print(f"  Batch size: {config.training.batch_size}")

# Validate configuration
try:
    issues = validate_config(config)
    if issues:
        print(f"❌ Configuration issues found: {issues}")
        raise ValueError(f"Invalid configuration: {issues}")
    else:
        print("✓ Configuration is valid!")
except Exception as e:
    print(f"❌ Configuration error: {e}")
    raise

# Performance optimizations
print(f"\n🚀 Performance Optimizations:")
print(f"  Model distributed across {num_gpus} GPUs")
print(f"  Increased batch size to {config.training.batch_size}")
print(f"  Using {dataset_config.num_proc} CPU cores for data processing")
print(f"  Flash Attention 2 enabled: {config.model.use_flash_attention}")
print(f"  Mixed precision: {config.model.torch_dtype}")

# Estimate training time
tokens_per_second_estimate = 1000  # Conservative estimate
estimated_time_seconds = config.training.total_training_tokens / tokens_per_second_estimate
estimated_hours = estimated_time_seconds / 3600

print(f"\n⏱️ Training Time Estimate:")
print(f"  Total tokens: {config.training.total_training_tokens:,}")
print(f"  Estimated tokens/sec: {tokens_per_second_estimate:,}")
print(f"  Estimated time: {estimated_hours:.1f} hours")
print(f"  Note: Actual time may vary based on hardware and batch size")

# Train the SAE
print(f"\n🏋️ Starting optimized SAE training...")
print(f"This training is optimized for multi-GPU setups but uses single-GPU SAE training.")
print(f"The base model is distributed across GPUs, which should improve memory efficiency.")

# Initialize trainer
trainer = SAETrainer(config)

# Add memory monitoring
def print_gpu_memory():
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1e9
            cached = torch.cuda.memory_reserved(i) / 1e9
            total = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"  GPU {i}: {allocated:.1f}GB allocated, {cached:.1f}GB cached, {total:.1f}GB total")

print(f"\n💾 Initial GPU Memory Usage:")
print_gpu_memory()

try:
    # Train the SAE
    sae = trainer.train()

    # Save the trained SAE
    save_path = f"llama_3_1_8b_layer{target_layer}_gated_sae_optimized.pt"
    trainer.save_model(save_path)

    print(f"\n✅ Training completed successfully!")
    print(f"📁 SAE saved to: {save_path}")

    # Get training metrics
    training_metrics = trainer.get_training_metrics()
    print(f"\n📊 Training Summary:")
    print(f"  Total steps: {training_metrics.get('total_steps', 'N/A')}")
    print(f"  Total tokens: {training_metrics.get('total_tokens', 'N/A'):,}")
    print(f"  Final loss: {training_metrics.get('final_loss', 'N/A')}")
    print(f"  Final MSE: {training_metrics.get('final_mse_loss', 'N/A')}")
    print(f"  Final L1: {training_metrics.get('final_l1_loss', 'N/A')}")
    print(f"  Final sparsity: {training_metrics.get('final_sparsity', 'N/A')}")

    print(f"\n💾 Final GPU Memory Usage:")
    print_gpu_memory()

except KeyboardInterrupt:
    print(f"\n⚠️ Training interrupted by user")
    print(f"💾 Current GPU Memory Usage:")
    print_gpu_memory()
except Exception as e:
    print(f"\n❌ Training failed: {e}")
    print(f"💾 GPU Memory Usage at failure:")
    print_gpu_memory()
    raise

print(f"\n🎉 Optimized SAE training completed!")
