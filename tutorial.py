import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
import logging

# ITAS imports
from itas import (
    SAEConfig,
    ModelConfig,
    DatasetConfig,
    TrainingConfig,
    SAETrainer,
    FunctionExtractor,
    RepresentationEngineer,
    SAEEvaluator,
    SAEVisualizer,
    UniversalModelLoader,
    DatasetManager,
    create_sae_config,
    validate_config,
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set CUDA devices
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
os.environ["CUDA_VISIBLE_DEVICES"] = "4,5,6,7"

# Check GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")
if device == "cuda":
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# Model configuration
model_name = "meta-llama/Llama-3.1-8B-Instruct"

# Note: You need HuggingFace access to LLaMA models
# Make sure you're logged in: huggingface-cli login

print(f"Loading {model_name}...")

# Load model and tokenizer with optimizations
model_loader = UniversalModelLoader(
    ModelConfig(
        model_name=model_name,
        use_flash_attention=True,  # Automatic compatibility detection
        torch_dtype="bfloat16",  # Memory efficient
        device_map="auto",  # Automatic device placement
        trust_remote_code=False,
    )
)

model, tokenizer = model_loader.load_model_and_tokenizer()

print(f"✓ Model loaded successfully!")
print(f"Model device: {next(model.parameters()).device}")
print(f"Model dtype: {next(model.parameters()).dtype}")

# Get detailed model information
model_info = model_loader.get_model_info()
hook_names = model_loader.get_hook_names()

print("📊 Model Information:")
print(f"  Model: {model_info['model_name']}")
print(f"  Architecture: {model_info['architecture']}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  Number of layers: {model_info['num_layers']}")
print(f"  Total parameters: {model_info['total_parameters']:,}")
print(f"  Vocabulary size: {model_info['vocab_size']:,}")

print("\n🔗 Available Hook Points:")
for hook_type, hooks in list(hook_names.items()):
    print(f"  {hook_type}: {len(hooks)} hooks")
    if hooks:
        print(f"    Example: {hooks}")

# Choose a middle layer for SAE training (good balance of complexity and interpretability)
target_layer = model_info["num_layers"] // 2
print(f"\n🎯 Target layer for SAE training: {target_layer}")

# Dataset configuration
dataset_config = DatasetConfig(
    dataset_name="wikitext",
    dataset_kwargs={"name": "wikitext-2-raw-v1"},  # Specify WikiText variant
    dataset_split="train",
    text_column="text",
    max_seq_length=2048,  # LLaMA 3.1 context length
    chunk_size=2048,
    streaming=False,  # Load full dataset for tutorial
    num_proc=4,  # Parallel processing
    trust_remote_code=False,
)

print("📚 Loading and preprocessing dataset...")

# Initialize dataset manager
dataset_manager = DatasetManager(dataset_config, tokenizer)

# Load and preprocess dataset
dataset = dataset_manager.load_dataset()
processed_dataset = dataset_manager.preprocess_dataset()

# Get dataset statistics
dataset_info = dataset_manager.get_dataset_info()
print(f"✓ Dataset loaded successfully!")
print(f" Dataset: {dataset_info['dataset_name']}")
print(f"  Raw size: {dataset_info['raw_size']:,} examples")
print(f"  Processed size: {dataset_info['processed_size']:,} examples")

total_tokens_val = dataset_info.get("total_tokens", "Unknown")
if isinstance(total_tokens_val, (int, float)):
    print(f"  Total tokens: {total_tokens_val:,}")
else:
    print(f"  Total tokens: {total_tokens_val}")

# Show a sample
sample = processed_dataset[0]
print(f"\n📝 Sample text (first 200 chars):")

input_ids_value = sample.get("input_ids")

if input_ids_value is None:
    print("'(Sample is missing input_ids)'")
# Check if it's a tensor-like object with an 'ndim' attribute (like PyTorch/TensorFlow tensors)
elif hasattr(input_ids_value, "ndim") and input_ids_value.ndim == 0:
    print(
        f"Debug: input_ids is a 0-dim tensor. Value: {input_ids_value.item() if hasattr(input_ids_value, 'item') else input_ids_value}"
    )
    # To decode a single token, it often needs to be in a sequence (e.g., a list or 1D tensor)
    # For PyTorch tensor:
    if hasattr(input_ids_value, "unsqueeze"):
        tokens_to_decode = input_ids_value.unsqueeze(
            0
        )  # Convert tensor(X) to tensor([X])
        print(
            f"'{tokenizer.decode(tokens_to_decode)}...' (Note: input_ids was a 0-dim tensor)"
        )
    else:  # Fallback if not a PyTorch tensor but still 0-dim somehow
        print(
            f"'{tokenizer.decode([input_ids_value])}...' (Note: input_ids was a 0-dim value, attempting decode)"
        )
elif (
    hasattr(input_ids_value, "__len__") and len(input_ids_value) > 0
):  # List or 1D tensor with elements
    tokens_to_show = input_ids_value[:50]
    print(f"'{tokenizer.decode(tokens_to_show)}...'")
else:  # Empty list, empty tensor, or other unexpected type
    print("'(Sample input_ids is empty or not in a decodable format)'")

# Get the correct hook names for this model
hook_names = model_loader.get_hook_names()
mlp_hook_pattern = hook_names["mlp_out"]
mlp_hook_name = mlp_hook_pattern.format(layer=target_layer)

print(f"🔗 Available hook patterns: {list(hook_names.keys())}")
print(f"🎯 Using MLP hook: {mlp_hook_name}")

# Create comprehensive SAE configuration
config = SAEConfig(
    # Model configuration
    model=ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        trust_remote_code=False,
        device_map="auto",
    ),
    # Dataset configuration
    dataset=dataset_config,
    # Training configuration
    training=TrainingConfig(
        total_training_tokens=50_000_000,  # 50M tokens for tutorial
        batch_size=4096,  # Adjust based on your GPU
        learning_rate=3e-4,
        l1_coefficient=1e-3,  # Sparsity regularization
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,
        # Checkpointing and logging
        checkpoint_every_n_tokens=10_000_000,
        save_checkpoint_dir="./checkpoints",
        log_every_n_steps=100,
        eval_every_n_tokens=5_000_000,
        # Weights & Biases logging (optional)
        use_wandb=False,  # Set to True if you want W&B logging
        wandb_project="spare-llama-tutorial",
    ),
    # SAE architecture
    architecture="gated",  # Start with gated SAE (best performance)
    expansion_factor=32,  # 32x expansion (4096 -> 131,072 features)
    hook_layer=target_layer,  # Middle layer
    hook_name=mlp_hook_name,  # Use correct hook name for this model
    activation_fn="relu",
    normalize_decoder=True,
    # Device and precision
    device=device,
    dtype="float32",  # Training precision
    seed=42,
)

print("\n⚙️ SAE Configuration:")
print(f"  Architecture: {config.architecture}")
print(f"  Hook layer: {config.hook_layer}")
print(f"  Hook name: {config.hook_name}")
print(f"  Expansion factor: {config.expansion_factor}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  SAE features: {model_info['hidden_size'] * config.expansion_factor:,}")
print(f"  Training tokens: {config.training.total_training_tokens:,}")
print(f"  Batch size: {config.training.batch_size}")

# Validate configuration
try:
    issues = validate_config(config)
    if issues:
        print(f"❌ Configuration issues found: {issues}")
        raise ValueError(f"Invalid configuration: {issues}")
    else:
        print("✓ Configuration is valid!")
except Exception as e:
    print(f"❌ Configuration error: {e}")
    raise

# Train the SAE
print("🏋️ Starting SAE training...")
print(f"Training configuration:")
print(f"  Total tokens: {config.training.total_training_tokens:,}")
print(f"  Batch size: {config.training.batch_size}")
print(f"  Learning rate: {config.training.learning_rate}")
print(f"  L1 coefficient: {config.training.l1_coefficient}")
print(f"  Architecture: {config.architecture}")
print(f"  Hook: {config.hook_name}")
print("\nThis may take 30-60 minutes depending on your hardware...\n")

# Initialize trainer
trainer = SAETrainer(config)

# Train the SAE
sae = trainer.train()

# Save the trained SAE
save_path = f"llama_3_1_8b_layer{target_layer}_gated_sae.pt"
trainer.save_model(save_path)

print(f"\n✅ Training completed successfully!")
print(f"📁 SAE saved to: {save_path}")

# Get training metrics
training_metrics = trainer.get_training_metrics()
print(f"\n📊 Training Summary:")
print(f"  Total steps: {training_metrics['total_steps']}")
print(f"  Total tokens: {training_metrics['total_tokens']:,}")
print(f"  Final loss: {training_metrics['final_loss']:.6f}")
print(f"  Final MSE: {training_metrics['final_mse_loss']:.6f}")
print(f"  Final L1: {training_metrics['final_l1_loss']:.6f}")
print(f"  Final sparsity: {training_metrics['final_sparsity']:.4f}")
print(f"  Final FVU: {training_metrics['final_fvu']:.4f}")
