#!/usr/bin/env python3
"""
Scalable SAE Training Tutorial

This script automatically detects available GPUs and uses the optimal training strategy:
- Single GPU: Efficient single-GPU training
- Multiple GPUs: Distributed training with DistributedDataParallel

Usage:
    python tutorial.py                    # Auto-detect and use all available GPUs
    CUDA_VISIBLE_DEVICES=0 python tutorial.py  # Force single GPU
    CUDA_VISIBLE_DEVICES=4,5,6,7 python tutorial.py  # Use specific GPUs
"""

import os
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
from torch.nn.parallel import DistributedDataParallel as DDP
import logging
from pathlib import Path

# ITAS imports
from itas import (
    SAEConfig,
    ModelConfig,
    DatasetConfig,
    TrainingConfig,
    UniversalModelLoader,
    DatasetManager,
    validate_config,
)
from itas.core.sae import TrainingSAE
from itas.core.activations_store import ActivationsStore

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_distributed(rank, world_size):
    """Initialize distributed training."""
    os.environ["MASTER_ADDR"] = "localhost"
    os.environ["MASTER_PORT"] = "12355"

    # Initialize the process group
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    torch.cuda.set_device(rank)


def cleanup_distributed():
    """Clean up distributed training."""
    dist.destroy_process_group()


def detect_gpu_setup():
    """Detect and configure GPU setup."""
    # Set CUDA devices if not already set
    if "CUDA_VISIBLE_DEVICES" not in os.environ:
        os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
        os.environ["CUDA_VISIBLE_DEVICES"] = "4,5,6,7"  # Default to GPUs 4-7

    # Check GPU availability
    device = "cuda" if torch.cuda.is_available() else "cpu"
    num_gpus = torch.cuda.device_count()

    print(f"🔧 GPU Setup:")
    print(f"  Using device: {device}")
    print(f"  Available GPUs: {num_gpus}")
    print(
        f"  CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}"
    )

    if device == "cuda":
        for i in range(num_gpus):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            print(
                f"    VRAM: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB"
            )

    return device, num_gpus


def create_config(rank=0, world_size=1):
    """Create SAE configuration for single or multi-GPU training."""

    # Model configuration
    model_name = "meta-llama/Llama-3.1-8B-Instruct"

    # Determine device mapping strategy
    if world_size == 1:
        # Single GPU: use balanced mapping for better memory distribution
        device_map = "balanced"
        device = "cuda:0"
    else:
        # Multi-GPU: each process gets one device
        device_map = {"": f"cuda:{rank}"}
        device = f"cuda:{rank}"

    model_config = ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        device_map=device_map,
        trust_remote_code=False,
    )

    # Dataset configuration
    dataset_config = DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"},
        dataset_split="train",
        text_column="text",
        max_seq_length=2048,
        chunk_size=2048,
        streaming=False,
        num_proc=4,
        trust_remote_code=False,
    )

    # Training configuration - adjust batch size for multi-GPU
    base_batch_size = 4096
    per_gpu_batch_size = (
        base_batch_size // world_size if world_size > 1 else base_batch_size * 2
    )

    training_config = TrainingConfig(
        total_training_tokens=50_000_000,  # 50M tokens
        batch_size=per_gpu_batch_size,
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,
        checkpoint_every_n_tokens=10_000_000,
        save_checkpoint_dir="./checkpoints",
        log_every_n_steps=100,
        eval_every_n_tokens=5_000_000,
        use_wandb=(rank == 0),  # Only log from rank 0
        wandb_project="llama-sae-scalable",
    )

    # SAE configuration
    config = SAEConfig(
        model=model_config,
        dataset=dataset_config,
        training=training_config,
        architecture="gated",
        expansion_factor=32,
        hook_layer=16,  # Will be updated after model loading
        hook_name="model.layers.{layer}.mlp.down_proj",  # Will be auto-detected
        activation_fn="relu",
        normalize_decoder=True,
        device=device,
        dtype="float32",
        seed=42,
    )

    return config, model_name


class ScalableSAETrainer:
    """Scalable SAE trainer that works with single or multiple GPUs."""

    def __init__(self, config, rank=0, world_size=1):
        self.config = config
        self.rank = rank
        self.world_size = world_size
        self.device = f"cuda:{rank}" if torch.cuda.is_available() else "cpu"

        # Initialize components
        self.model = None
        self.tokenizer = None
        self.sae = None
        self.optimizer = None
        self.dataset_manager = None

    def setup(self):
        """Setup model, SAE, and data components."""
        if self.rank == 0:
            print(
                f"🔧 Setting up {'multi-GPU' if self.world_size > 1 else 'single-GPU'} SAE training..."
            )

        # Load model and tokenizer
        model_loader = UniversalModelLoader(self.config.model)
        self.model, self.tokenizer = model_loader.load_model_and_tokenizer()

        # Get model info and update config
        model_info = model_loader.get_model_info()
        hook_names = model_loader.get_hook_names()

        # Update target layer and hook name
        target_layer = model_info["num_layers"] // 2
        self.config.hook_layer = target_layer

        if "mlp_out" in hook_names:
            mlp_hook_pattern = hook_names["mlp_out"]
            self.config.hook_name = mlp_hook_pattern.format(layer=target_layer)

        if self.rank == 0:
            print(f"✓ Model loaded: {model_info['model_name']}")
            print(f"  Architecture: {model_info['architecture']}")
            print(f"  Hidden size: {model_info['hidden_size']}")
            print(f"  Layers: {model_info['num_layers']}")
            print(f"  Target layer: {target_layer}")
            print(f"  Hook name: {self.config.hook_name}")

        # Setup dataset
        self.dataset_manager = DatasetManager(self.config.dataset, self.tokenizer)
        self.dataset_manager.load_dataset()
        processed_dataset = self.dataset_manager.preprocess_dataset()

        # Shard dataset across GPUs if multi-GPU
        if self.world_size > 1:
            dataset_size = len(processed_dataset)
            per_gpu_size = dataset_size // self.world_size
            start_idx = self.rank * per_gpu_size
            end_idx = (
                start_idx + per_gpu_size
                if self.rank < self.world_size - 1
                else dataset_size
            )
            self.dataset_manager.dataset = processed_dataset.select(
                range(start_idx, end_idx)
            )

            if self.rank == 0:
                print(f"✓ Dataset sharded across {self.world_size} GPUs")
                print(f"  Total samples: {dataset_size:,}")
                print(f"  Per GPU: {per_gpu_size:,}")
        else:
            if self.rank == 0:
                dataset_info = self.dataset_manager.get_dataset_info()
                print(f"✓ Dataset loaded: {dataset_info['processed_size']:,} samples")

        # Initialize SAE
        d_in = model_info["hidden_size"]

        self.sae = TrainingSAE(
            d_in=d_in,
            d_sae=d_in * self.config.expansion_factor,
            architecture=self.config.architecture,
            activation_fn=self.config.activation_fn,
            normalize_decoder=self.config.normalize_decoder,
            device=self.device,
            dtype=getattr(torch, self.config.dtype),
        )

        # Wrap SAE with DDP if multi-GPU
        if self.world_size > 1:
            self.sae = DDP(self.sae, device_ids=[self.rank])

        # Initialize optimizer
        self.optimizer = torch.optim.Adam(
            self.sae.parameters(),
            lr=self.config.training.learning_rate,
            betas=(0.9, 0.999),
            weight_decay=0.0,
        )

        if self.rank == 0:
            print(f"✓ SAE initialized")
            print(f"  d_in: {d_in}")
            print(f"  d_sae: {d_in * self.config.expansion_factor}")
            print(f"  Architecture: {self.config.architecture}")
            print(f"  Multi-GPU: {self.world_size > 1}")

    def train(self):
        """Train the SAE."""
        if self.rank == 0:
            print("🏋️ Starting SAE training...")

        # Setup activations store
        activations_store = ActivationsStore(
            self.model,
            self.tokenizer,
            self.config,
            self.dataset_manager,
        )

        # Training parameters
        total_tokens = self.config.training.total_training_tokens
        batch_size = self.config.training.batch_size
        total_steps = total_tokens // (batch_size * self.world_size)

        if self.rank == 0:
            print(f"  Total tokens: {total_tokens:,}")
            print(f"  Batch size per GPU: {batch_size}")
            print(f"  Total steps: {total_steps:,}")
            print(f"  World size: {self.world_size}")

            # Estimate training time
            tokens_per_second_estimate = 1000 * self.world_size  # Scale with GPUs
            estimated_hours = total_tokens / tokens_per_second_estimate / 3600
            print(f"  Estimated time: {estimated_hours:.1f} hours")

        # Training loop
        step = 0
        tokens_processed = 0

        try:
            with activations_store:
                activations_store.start_streaming(batch_size=32)

                while tokens_processed < total_tokens and step < total_steps:
                    # Get batch of activations
                    activations = activations_store.get_next_batch(timeout=10.0)
                    if activations is None:
                        continue

                    # Move to device and prepare
                    activations = activations.to(self.device).float()
                    if len(activations.shape) > 2:
                        activations = activations.view(-1, activations.shape[-1])

                    # Limit batch size
                    if activations.shape[0] > batch_size:
                        activations = activations[:batch_size]

                    # Training step
                    self.optimizer.zero_grad()

                    # Forward pass
                    if self.world_size > 1:
                        output = self.sae(activations)
                    else:
                        output = self.sae.training_forward(
                            activations,
                            l1_coefficient=self.config.training.l1_coefficient,
                        )

                    # Compute loss
                    if self.world_size > 1:
                        l1_coeff = self.config.training.l1_coefficient
                        total_loss = output.mse_loss + l1_coeff * output.l1_loss
                    else:
                        total_loss = output.mse_loss + output.l1_loss
                        if output.aux_loss is not None:
                            total_loss = total_loss + output.aux_loss

                    # Backward pass
                    total_loss.backward()

                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(self.sae.parameters(), max_norm=1.0)

                    # Optimizer step
                    self.optimizer.step()

                    # Update counters
                    step += 1
                    tokens_processed += activations.shape[0] * self.world_size

                    # Logging (only from rank 0)
                    if (
                        self.rank == 0
                        and step % self.config.training.log_every_n_steps == 0
                    ):
                        print(
                            f"Step {step:,}/{total_steps:,} | "
                            f"Tokens: {tokens_processed:,}/{total_tokens:,} | "
                            f"Loss: {total_loss.item():.6f} | "
                            f"MSE: {output.mse_loss.item():.6f} | "
                            f"L1: {output.l1_loss.item():.6f}"
                        )

                activations_store.stop_streaming()

        except Exception as e:
            logger.error(f"Training failed on rank {self.rank}: {e}")
            raise

        if self.rank == 0:
            print("✅ Training completed!")

        return self.sae.module if self.world_size > 1 else self.sae


def train_worker(rank, world_size):
    """Worker function for distributed training."""
    try:
        # Setup distributed training if multi-GPU
        if world_size > 1:
            setup_distributed(rank, world_size)

        # Create configuration
        config, _ = create_config(rank, world_size)

        # Validate configuration (only on rank 0)
        if rank == 0:
            issues = validate_config(config)
            if issues:
                print(f"❌ Configuration issues: {issues}")
                return
            print("✓ Configuration validated")

        # Initialize trainer
        trainer = ScalableSAETrainer(config, rank, world_size)
        trainer.setup()

        # Train SAE
        sae = trainer.train()

        # Save model (only from rank 0)
        if rank == 0:
            target_layer = config.hook_layer
            save_path = f"llama_3_1_8b_layer{target_layer}_gated_sae_scalable.pt"
            torch.save(
                {
                    "sae_state_dict": sae.state_dict(),
                    "config": config.to_dict(),
                },
                save_path,
            )
            print(f"📁 SAE saved to: {save_path}")

    except Exception as e:
        logger.error(f"Worker {rank} failed: {e}")
        raise
    finally:
        if world_size > 1:
            cleanup_distributed()


def main():
    """Main function to launch single or multi-GPU training."""
    # Detect GPU setup
    device, num_gpus = detect_gpu_setup()

    if device == "cpu":
        print("❌ CUDA not available. This tutorial requires GPU.")
        return

    print(f"\n🚀 Launching scalable SAE training")
    print(f"  Available GPUs: {num_gpus}")
    print(
        f"  Strategy: {'Multi-GPU (DistributedDataParallel)' if num_gpus > 1 else 'Single-GPU (Optimized)'}"
    )

    if num_gpus == 1:
        # Single GPU training
        print(
            "📝 Note: For multi-GPU training, set CUDA_VISIBLE_DEVICES to multiple GPUs"
        )
        train_worker(0, 1)
    else:
        # Multi-GPU training
        print(f"🔥 Using {num_gpus} GPUs for distributed training")
        mp.spawn(train_worker, args=(num_gpus,), nprocs=num_gpus, join=True)


if __name__ == "__main__":
    main()
